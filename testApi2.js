const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();

const PORT = 3052;
const LOG_FILE_PATH = path.join(__dirname, 'logs2.txt');

// Middleware to parse JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// POST /auth/token - generates access tokens for testing
app.post('/auth/token', (req, res) => {
  const { client_id, client_secret, grant_type, scope } = req.body;

  // Validate required fields
  if (!client_id || !client_secret || !grant_type) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'Missing required parameters: client_id, client_secret, grant_type'
    });
  }

  // Validate client credentials (for testing purposes)
  if (client_id !== 'caremate_gov_client' || client_secret !== 'super_secret_key_123') {
    return res.status(401).json({
      error: 'invalid_client',
      error_description: 'Invalid client credentials'
    });
  }

  // Validate grant type
  if (grant_type !== 'client_credentials') {
    return res.status(400).json({
      error: 'unsupported_grant_type',
      error_description: 'Only client_credentials grant type is supported'
    });
  }

  // Generate a mock access token
  const accessToken = `gov_token_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const expiresIn = 3600; // 1 hour

  console.log(`🔐 Generated access token for client: ${client_id}, scope: ${scope || 'default'}`);

  res.status(200).json({
    access_token: accessToken,
    token_type: 'Bearer',
    expires_in: expiresIn,
    scope: scope || 'employee_data_write'
  });
});

// POST /log - logs incoming JSON data to logs.txt
app.post('/log', (req, res) => {
  const data = req.body;
  const authHeader = req.headers.authorization;

  // Check for Bearer token (for api_2_outbound testing)
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    console.log(`🔑 Received request with Bearer token: ${token.substring(0, 20)}...`);
  }

  if (!data || Object.keys(data).length === 0) {
    return res.status(400).json({ error: 'No data provided' });
  }

  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - ${JSON.stringify(data)}\n`;
  console.log(`📝 Logging data: ${logEntry.substring(0, 100)}...`);

  fs.appendFile(LOG_FILE_PATH, logEntry, (err) => {
    if (err) {
      console.error('Failed to write to log file:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    res.status(200).json({
      message: 'Data logged successfully',
      timestamp: timestamp,
      recordCount: Array.isArray(data.employees) ? data.employees.length : 1
    });
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
