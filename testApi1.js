const express = require('express');
const fs = require('fs');
const path = require('path');
const app = express();

const PORT = 3051;
const LOG_FILE_PATH = path.join(__dirname, 'logs1.txt');

// Middleware to parse JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// POST /log - logs incoming JSON data to logs.txt
app.post('/log', (req, res) => {
  const data = req.body;

  if (!data || Object.keys(data).length === 0) {
    return res.status(400).json({ error: 'No data provided' });
  }

  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - ${JSON.stringify(data)}\n`;
  console.log(logEntry);

  fs.appendFile(LOG_FILE_PATH, logEntry, (err) => {
    if (err) {
      console.error('Failed to write to log file:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    res.status(200).json({ message: 'Data logged successfully' });
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
