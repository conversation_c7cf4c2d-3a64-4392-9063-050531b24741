const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { getAgentSettings } = require('../utils/caching');

// Token cache to store generated tokens
const tokenCache = new Map();

/**
 * Generates or retrieves authentication token
 * @param {object} settings - Agent settings
 * @param {string} agentId - Agent ID for caching
 * @returns {Promise<string>} - Authentication token
 */
const getAuthToken = async (settings, agentId) => {
  // Case 1: Token is directly provided in settings
  if (settings.api_token || settings.token) {
    return settings.api_token || settings.token;
  }

  // Case 2: Token needs to be generated from credentials
  if (settings.token_url && (settings.username || settings.client_id)) {
    const cacheKey = `token_${agentId}`;
    const cachedToken = tokenCache.get(cacheKey);

    // Check if cached token is still valid
    if (cachedToken && cachedToken.expiresAt > Date.now()) {
      logger.debug(`[API Handler] Using cached token for agent ${agentId}`);
      return cachedToken.token;
    }

    try {
      logger.info(`[API Handler] Generating new token for agent ${agentId}`);

      // Prepare token request
      const tokenRequestData = {};

      // Support different authentication methods
      if (settings.grant_type) {
        tokenRequestData.grant_type = settings.grant_type;
      } else {
        tokenRequestData.grant_type = 'client_credentials'; // default
      }

      if (settings.client_id) {
        tokenRequestData.client_id = settings.client_id;
      }

      if (settings.client_secret) {
        tokenRequestData.client_secret = settings.client_secret;
      }

      if (settings.username) {
        tokenRequestData.username = settings.username;
      }

      if (settings.password) {
        tokenRequestData.password = settings.password;
      }

      if (settings.scope) {
        tokenRequestData.scope = settings.scope;
      }

      // Make token request
      const tokenResponse = await axios({
        method: 'POST',
        url: settings.token_url,
        data: tokenRequestData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...(settings.token_headers ? JSON.parse(settings.token_headers) : {})
        },
        // timeout: settings.token_timeout || 30000
      });

      if (tokenResponse.status !== 200) {
        throw new Error(`Token request failed with status ${tokenResponse.status}`);
      }

      const tokenData = tokenResponse.data;
      const token = tokenData.access_token || tokenData.token;

      if (!token) {
        throw new Error('No access token found in response');
      }

      // Calculate expiration time (default to 1 hour if not provided)
      const expiresIn = tokenData.expires_in || 3600; // seconds
      const expiresAt = Date.now() + (expiresIn * 1000) - 60000; // Subtract 1 minute for safety

      // Cache the token
      tokenCache.set(cacheKey, {
        token,
        expiresAt,
        generatedAt: Date.now()
      });

      logger.info(`[API Handler] Token generated successfully for agent ${agentId}, expires in ${expiresIn} seconds`);

      return token;

    } catch (error) {
      logger.error(`[API Handler] Failed to generate token for agent ${agentId}:`, error.message);
      throw new Error(`Token generation failed: ${error.message}`);
    }
  }

  // Case 3: No token configuration found
  // logger.warn(`[API Handler] No token configuration found for agent ${agentId}`);
  return null;
};

/**
 * Transforms template string with data values
 * @param {string} template - Template string with {{field}} placeholders
 * @param {object} data - Data object to extract values from
 * @param {object} context - Additional context data (batch_id, timestamp, etc.)
 * @returns {string} - Transformed string
 */
const transformTemplate = (template, data, context = {}) => {
  if (typeof template !== 'string') return template;
  
  return template.replace(/\{\{([^}]+)\}\}/g, (match, field) => {
    // Handle special context fields
    if (field === 'current_timestamp') {
      return new Date().toISOString();
    }
    if (field === 'batch_id') {
      return context.batchId || uuidv4();
    }
    if (field === 'batch_size') {
      return context.batchSize || 1;
    }
    
    // Handle nested field access (e.g., Identity.email)
    const fieldParts = field.split('.');
    let value = data;
    
    for (const part of fieldParts) {
      value = value?.[part];
      if (value === undefined) break;
    }
    
    return value !== undefined ? value : '';
  });
};

/**
 * Recursively transforms an object using template transformation
 * @param {any} obj - Object to transform
 * @param {object} data - Data for transformation
 * @param {object} context - Context data
 * @returns {any} - Transformed object
 */
const transformObject = (obj, data, context = {}) => {
  if (typeof obj === 'string') {
    return transformTemplate(obj, data, context);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => transformObject(item, data, context));
  }
  
  if (obj && typeof obj === 'object') {
    const transformed = {};
    for (const [key, value] of Object.entries(obj)) {
      transformed[key] = transformObject(value, data, context);
    }
    return transformed;
  }
  
  return obj;
};

/**
 * Validates data against mapping rules
 * @param {object} data - Data to validate
 * @param {object} validation - Validation rules
 * @returns {object} - Validation result
 */
const validateData = (data, validation) => {
  const errors = [];

  if (!validation) {
    return { isValid: true, errors: [] };
  }

  // Check required fields
  if (validation.required) {
    for (const field of validation.required) {
      const fieldParts = field.split('.');
      let value = data;
      let currentPath = '';

      for (let i = 0; i < fieldParts.length; i++) {
        const part = fieldParts[i];
        currentPath = currentPath ? `${currentPath}.${part}` : part;

        if (part.includes('*')) {
          // Handle array validation (e.g., employees.*.email)
          const arrayField = part.replace('*', '');
          if (Array.isArray(value)) {
            let hasValidItems = false;
            for (let j = 0; j < value.length; j++) {
              const remainingPath = fieldParts.slice(i + 1).join('.');
              if (remainingPath) {
                // Check nested field in array item
                let nestedValue = value[j];
                const nestedParts = remainingPath.split('.');
                for (const nestedPart of nestedParts) {
                  nestedValue = nestedValue?.[nestedPart];
                }
                if (nestedValue !== undefined && nestedValue !== null && nestedValue !== '') {
                  hasValidItems = true;
                }
              } else if (value[j] && value[j][arrayField] !== undefined && value[j][arrayField] !== null && value[j][arrayField] !== '') {
                hasValidItems = true;
              }
            }
            if (!hasValidItems) {
              errors.push(`Required field ${field} is missing in all array items`);
            }
          } else {
            errors.push(`Expected array for field ${currentPath.replace('.*', '')} but got ${typeof value}`);
          }
          break;
        } else if (!isNaN(part)) {
          // Handle array index (e.g., employees.0.employeeId)
          const index = parseInt(part);
          if (Array.isArray(value)) {
            if (index >= value.length) {
              errors.push(`Array index ${index} out of bounds for field ${currentPath}`);
              break;
            }
            value = value[index];
          } else {
            errors.push(`Expected array for field ${currentPath.replace(`.${part}`, '')} but got ${typeof value}`);
            break;
          }
        } else {
          value = value?.[part];
        }

        // Check if we've reached the end and the value is missing
        if (i === fieldParts.length - 1) {
          if (value === undefined || value === null || value === '') {
            errors.push(`Required field ${field} is missing`);
          }
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Makes HTTP request with retry logic
 * @param {object} config - Request configuration
 * @param {object} performanceMonitor - Performance monitor instance
 * @returns {Promise<object>} - Response data
 */
const makeApiRequest = async (config, performanceMonitor = null) => {
  const { url, method, headers, data, 
    // timeout, 
    // retries = 3, retryDelay = 1000, retryBackoff = 'linear' 
  } = config;
  
  let lastError;
  
  // for (let attempt = 1; attempt <= retries + 1; attempt++) {
    // try {
      const requestStart = Date.now();

      const response = await axios({
        url,
        method,
        headers,
        data,
        // timeout,
        validateStatus: (status) => status < 600 // Don't throw for HTTP error status codes
      });

      const requestTime = Date.now() - requestStart;

      // Record API metrics in performance monitor
      performanceMonitor?.logProgress(`API request completed`, {
        // attempt,
        status: response.status,
        requestTime,
        success: response.status >= 200 && response.status < 300,
        url: url.replace(/\/\/.*@/, '//***@') // Hide credentials in logs
      });

      
      return {
        success: response.status >= 200 && response.status < 300,
        status: response.status,
        data: response.data,
        headers: response.headers,
        requestTime
      };
      
    // } catch (error) {
    //   lastError = error;
    //   const requestTime = Date.now() - (error.config?.metadata?.startTime || Date.now());

    //   // Record failed API metrics
    //   performanceMonitor?.logProgress(`API request failed`, {
    //     // attempt,
    //     error: error.message,
    //     requestTime,
    //     statusCode: error.response?.status,
    //     timeout: error.code === 'ECONNABORTED',
    //     url: url.replace(/\/\/.*@/, '//***@')
    //   });

    //   // logger.warn(`API request attempt ${attempt} failed:`, {
    //   //   error: error.message,
    //   //   url: url.replace(/\/\/.*@/, '//***@'),
    //   //   requestTime
    //   // });
      
    //   // Don't retry on the last attempt
    //   // if (attempt <= retries) {
    //   //   const delay = retryBackoff === 'exponential' 
    //   //     ? retryDelay * Math.pow(2, attempt - 1)
    //   //     : retryDelay;
          
    //   //   logger.info(`Retrying API request in ${delay}ms (attempt ${attempt + 1}/${retries + 1})`);
    //   //   await new Promise(resolve => setTimeout(resolve, delay));
    //   // }
    // }
  // }
  
  // All retries failed
  // throw new Error(`API request failed after ${retries + 1} attempts: ${lastError.message}`);
};

/**
 * Main handler for sending data to external APIs
 * @param {object} params - Handler parameters
 * @param {object} params.event - Event data from queue
 * @param {object} params.agent - Agent configuration
 * @param {object} params.performanceMonitor - Performance monitor instance
 * @returns {Promise<object>} - Handler results
 */
const sendApiData = async ({ event, agent, performanceMonitor = null }) => {
  const handlerResults = {
    agentName: agent.name,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    apiCalls: 0,
    errors: []
  };

  try {
    logger.info(`[API Handler] Processing event for agent: ${agent.name}`);
    
    // Step 1: Load mapping configuration   
    const mappingConfig = require(`../mappings/${agent.mapping}.mapping.json`);
    
    // Step 2: Get agent settings for API configuration
    const settings = await getAgentSettings(agent.agent_id);
    
    // Step 3: Process event data
    const eventData = event.params || event.data || event;
    const batchId = uuidv4();
    const context = {
      batchId,
      batchSize: Array.isArray(eventData) ? eventData.length : 1,
      timestamp: new Date().toISOString()
    };

    handlerResults.totalRecords = Array.isArray(eventData) ? eventData.length : 1;

    // Step 4: Prepare data for transformation based on mapping type
    let dataForTransformation;
    let transformedData;

    // Check if this is an array-based mapping (like externalPartnerApi)
    const isArrayMapping = mappingConfig.dataTransform.properties.employees &&
                          Array.isArray(mappingConfig.dataTransform.properties.employees);

    if (isArrayMapping) {
      // For array-based mappings, handle multiple records
      if (Array.isArray(eventData)) {
        // Transform each item in the array
        dataForTransformation = eventData.map(item => ({ Identity: item }));
      } else {
        // Single item, wrap in array
        dataForTransformation = [{ Identity: eventData }];
      }

      logger.debug(`[API Handler] Array mapping - Data structure for transformation:`, {
        originalDataType: Array.isArray(eventData) ? 'array' : 'object',
        transformationDataLength: dataForTransformation.length
      });

      // Step 5: Transform data according to mapping
      // const transformStepName = `[Trace ID: ${event.trace_id}] Data Transformation - ${agent.name}`;
      // performanceMonitor?.startStep(transformStepName, {
      //   recordCount: handlerResults.totalRecords,
      //   batchId
      // });

      // For array mappings, we need to transform each employee record
      const employeeTemplate = mappingConfig.dataTransform.properties.employees[0];
      const transformedEmployees = dataForTransformation.map(data =>
        transformObject(employeeTemplate, data, context)
      );

      // Build the complete transformed structure
      transformedData = {
        employees: transformedEmployees,
        batchInfo: transformObject(mappingConfig.dataTransform.properties.batchInfo, {}, context)
      };

      // performanceMonitor?.endStep(transformStepName, {
      //   transformedEmployees: transformedEmployees.length,
      //   transformedFields: Object.keys(transformedData).length
      // });

    } else {
      // For single object mappings (like apiOutbound)
      if (Array.isArray(eventData)) {
        // Take the first item if it's an array
        dataForTransformation = { Identity: eventData[0] };
      } else {
        // Single object, wrap in Identity key
        dataForTransformation = { Identity: eventData };
      }

      logger.debug(`[API Handler] Object mapping - Data structure for transformation:`, {
        originalDataType: Array.isArray(eventData) ? 'array' : 'object',
        originalDataKeys: Array.isArray(eventData) ? 'array' : Object.keys(eventData),
        transformationDataKeys: Object.keys(dataForTransformation)
      });

      // Step 5: Transform data according to mapping
      // const transformStepName = `[Trace ID: ${event.trace_id}] Data Transformation - ${agent.name}`;
      // performanceMonitor?.startStep(transformStepName, {
      //   recordCount: handlerResults.totalRecords,
      //   batchId
      // });

      transformedData = transformObject(mappingConfig.dataTransform.properties, dataForTransformation, context);
      // performanceMonitor?.endStep(transformStepName, {
      //   transformedFields: Object.keys(transformedData).length
      // });
    }

    logger.info(`[API Handler] Transformed data for ${agent.name}:`, JSON.stringify(transformedData, null, 2));

    // Step 6: Validate transformed data
    logger.debug(`[API Handler] Validating transformed data:`, {
      dataKeys: Object.keys(transformedData),
      validationRules: mappingConfig.validation?.required || []
    });

    const validation = validateData(transformedData, mappingConfig.validation);
    if (!validation.isValid) {
      logger.error(`[API Handler] Data validation failed for ${agent.name}:`, validation.errors);
      throw new Error(`Data validation failed: ${validation.errors.join(', ')}`);
    }

    logger.info(`[API Handler] Data validation passed for ${agent.name}`);

    // Step 7: Get authentication token if needed
    const authToken = await getAuthToken(settings, agent.agent_id);

    // Step 8: Prepare API request
    const contextWithAuth = {
      ...settings,
      ...context,
      auth_token: authToken,
      api_token: authToken // For backward compatibility
    };

    const apiConfig = {
      url: settings.api_url || settings.url,
      method: mappingConfig.apiConfig.method,
      headers: transformObject(mappingConfig.apiConfig.headers, {}, contextWithAuth),
      data: transformedData,
      timeout: mappingConfig.apiConfig.timeout,
      retries: mappingConfig.apiConfig.retries,
      retryDelay: mappingConfig.apiConfig.retryDelay,
      retryBackoff: mappingConfig.apiConfig.retryBackoff
    };
    
    // Step 9: Make API call
    // const apiStepName = `[Trace ID: ${event.trace_id}] API Request - ${agent.name}`;
    // performanceMonitor?.startStep(apiStepName, {
    //   url: apiConfig.url.replace(/\/\/.*@/, '//***@'),
    //   method: apiConfig.method,
    //   dataSize: JSON.stringify(apiConfig.data).length,
    //   hasAuthToken: !!authToken
    // });
    
    logger.info(`[API Handler] Making API call to ${apiConfig.url} for agent ${agent.name}`);

    let response;
    try {
      response = await makeApiRequest(apiConfig, performanceMonitor);
      logger.info(`[API Handler] API call successful for ${agent.name}:`, {
        status: response.status,
        success: response.success
      });
    } catch (apiError) {
      logger.error(`[API Handler] API call failed for ${agent.name}:`, apiError);
      throw apiError;
    }

    handlerResults.apiCalls = 1;
    
    // performanceMonitor?.endStep(apiStepName, {
    //   success: response.success,
    //   status: response.status,
    //   requestTime: response.requestTime,
    //   responseSize: JSON.stringify(response.data).length
    // });
    
    if (response.success) {
      handlerResults.successfulRecords = handlerResults.totalRecords;
      logger.info(`[API Handler] Successfully sent ${handlerResults.totalRecords} records to ${agent.name}`);
    } else {
      handlerResults.failedRecords = handlerResults.totalRecords;
      handlerResults.errors.push(`API call failed with status ${response.status}`);
      logger.error(`[API Handler] API call failed for ${agent.name}:`, response.data);
    }
    
    return {
      ...handlerResults,
      response: response.data,
      status: response.status
    };
    
  } catch (error) {
    handlerResults.failedRecords = handlerResults.totalRecords;
    handlerResults.errors.push(error.message);
    
    logger.error(`[API Handler] Error processing event for agent ${agent.name}:`, error);
    throw error;
  }
};

module.exports = sendApiData;
