'use strict';
const { v4: uuidv4 } = require('uuid');
const { encryptAgentSetting } = require('../utils/encryption');

module.exports = {
  up: async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      const now = new Date();

      // Helper function to create encrypted setting
      const createSetting = (agentId, key, value, keyName, description) => {
        const { value: encryptedValue, isEncrypted } = encryptAgentSetting(key, value);
        return {
          agent_setting_id: uuidv4(),
          agent_id: agentId,
          key,
          value: encryptedValue,
          key_name: keyName,
          description,
          is_encrypted: isEncrypted,
          created_at: now,
          updated_at: now,
        };
      };

      // Define all agent configurations with their settings
      const agentConfigs = [
        {
          agent_id: uuidv4(),
          name: 'ftp_connection_main',
          display_name: 'Main FTP Server',
          description: 'Primary FTP server for HR data',
          source: 'FileTransfer',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 100,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            host: 'ftp1.company.com',
            port: '21',
            username: 'ftp1_user',
            password: 'ftp1_secure_pass_2024',
            path: '/data/hr/incoming'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'ftp_connection_backup',
          display_name: 'Backup FTP Server',
          description: 'Secondary FTP server for HR data',
          source: 'FileTransfer',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 15,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            host: 'ftp2.company.com',
            port: '21',
            username: 'ftp2_user',
            password: 'ftp2_secure_pass_2024',
            path: '/backup/hr/files'
          }
        },
        {
          agent_id: uuidv4(),
          name: 's3_connection_production',
          display_name: 'Production S3 Bucket',
          description: 'Main production S3 bucket',
          source: 'AWSS3',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 20,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            access_key_id: 'AKIAIOSFODNN7PROD1',
            secret_access_key: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYPROD1KEY',
            bucket_name: 'production-hr-bucket-1',
            region: 'us-east-1'
          }
        },
        {
          agent_id: uuidv4(),
          name: 's3_connection_archive',
          display_name: 'Archive S3 Bucket',
          description: 'Archive S3 bucket for old data',
          source: 'AWSS3',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 25,
          status: false,
          handler: 'generateCsv',
          type: 'Outbound',
          settings: {
            access_key_id: 'AKIAIOSFODNN7ARCH1',
            secret_access_key: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYARCH1KEY',
            bucket_name: 'archive-hr-bucket-1',
            region: 'us-west-2'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'azure_connection_production',
          display_name: 'Production Azure Storage',
          description: 'Main Azure blob storage',
          source: 'AzureBlob',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 18,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            connection_string: 'DefaultEndpointsProtocol=https;AccountName=prodaccount1;AccountKey=prodkey123456789abc;EndpointSuffix=core.windows.net',
            container_name: 'production-hr-files'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'azure_connection_backup',
          display_name: 'Backup Azure Storage',
          description: 'Backup Azure blob storage',
          source: 'AzureBlob',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 22,
          status: false,
          handler: 'generateCsv',
          type: 'Outbound',
          settings: {
            connection_string: 'DefaultEndpointsProtocol=https;AccountName=backupaccount1;AccountKey=backupkey987654321def;EndpointSuffix=core.windows.net',
            container_name: 'backup-hr-files'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'local_connection_batch_100',
          display_name: 'HR Local Directory',
          description: 'Local directory for HR files',
          source: 'Local',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 100,
          status: true,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            directory_path: 'E:\\Windows\\Desktop\\care\\csv\\batch100'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'local_connection_batch_10',
          display_name: 'HR Data Local Directory',
          description: 'Local directory for archived files',
          source: 'Local',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 10,
          status: true,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            directory_path: 'E:\\Windows\\Desktop\\care\\csv\\batch10'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'url_connection_external_api',
          display_name: 'External API Endpoint',
          description: 'External partner API',
          source: 'URL',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 5,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            url: 'https://api.external-partner.com/data/hr-export.csv'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'url_connection_government_api',
          display_name: 'Government Data API',
          description: 'Government data API endpoint',
          source: 'URL',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 3,
          status: false,
          handler: 'sendHrData',
          type: 'Inbound',
          settings: {
            url: 'https://government-data.gov/api/public-health-data.csv'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'local_connection_outbound_batch_100',
          display_name: 'HR Data Outbound Local Directory',
          description: 'Local directory for outbound CSV generation',
          source: 'Local',
          queue: 'hr_csv_data',
          mapping: 'hrData',
          stagging_key: "email",
          batch_size: 100,
          status: true,
          handler: 'generateCsv',
          type: 'Outbound',
          settings: {
            directory_path: 'E:\\Windows\\Desktop\\care\\csv\\outbound'
          }
        },
        {
          agent_id: uuidv4(),
          name: 'api_1_outbound',
          display_name: 'External Partner API Outbound',
          description: 'Send employee data to external partner via API',
          source: 'API',
          queue: 'api_1_outbound_queue',
          mapping: 'api1Outbound',
          stagging_key: "email",
          batch_size: 50,
          status: true,
          handler: 'sendApiData',
          type: 'Outbound',
          settings: {
            api_url: 'http://localhost:3051/log',
            api_key: 'your_api_key_here',
            client_id: 'caremate_client',
            timeout: 45000,
            retries: 0
          }
        },
        {
          agent_id: uuidv4(),
          name: 'api_2_outbound',
          display_name: 'Government System API Outbound',
          description: 'Send employee data to government system via API with token authentication',
          source: 'API',
          queue: 'api_2_outbound_queue',
          mapping: 'api2Outbound',
          stagging_key: "email",
          batch_size: 25,
          status: true,
          handler: 'sendApiData',
          type: 'Outbound',
          settings: {
            api_url: 'http://localhost:3052/log',
            token_url: 'http://localhost:3052/auth/token',
            client_id: 'caremate_gov_client',
            client_secret: 'super_secret_key_123',
            grant_type: 'client_credentials',
            scope: 'employee_data_write',
            timeout: 60000,
            retries: 5,
            token_timeout: 30000
          }
        }
      ];

      // Prepare agent data for bulk insert
      const agentData = agentConfigs.map(config => ({
        agent_id: config.agent_id,
        name: config.name,
        display_name: config.display_name,
        description: config.description,
        source: config.source,
        mapping: config.mapping,
        queue: config.queue,
        stagging_key: config.stagging_key,
        batch_size: config.batch_size,
        status: config.status,
        handler: config.handler,
        type: config.type,
        created_at: now,
        updated_at: now,
      }));

      // Insert agents
      await queryInterface.bulkInsert('agent', agentData, { transaction });

      // Prepare agent settings data
      const agentSettingsData = [];

      // Description mapping for settings
      const descriptions = {
        host: 'FTP/SFTP server hostname',
        port: 'FTP/SFTP server port',
        username: 'FTP/SFTP username',
        password: 'FTP/SFTP password',
        path: 'Remote directory path',
        access_key_id: 'AWS S3 access key ID',
        secret_access_key: 'AWS S3 secret access key',
        bucket_name: 'AWS S3 bucket name',
        region: 'AWS S3 region',
        connection_string: 'Azure Blob Storage connection string',
        container_name: 'Azure Blob container name',
        directory_path: 'Local directory path to monitor',
        url: 'HTTP/HTTPS URL to download CSV file',
        api_url: 'API endpoint URL for outbound data transmission',
        api_key: 'API key for authentication',
        api_token: 'API token for authentication',
        client_id: 'Client ID for API authentication',
        timeout: 'API request timeout in milliseconds',
        retries: 'Number of retry attempts for failed API calls'
      };

      // Create settings for each agent
      for (const config of agentConfigs) {
        // Add all settings
        Object.entries(config.settings).forEach(([key, value]) => {
          const keyName = key.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' ');

          agentSettingsData.push(
            createSetting(
              config.agent_id,
              key,
              value,
              keyName,
              descriptions[key] || `${keyName} setting`
            )
          );
        });
      }

      // Insert agent settings
      await queryInterface.bulkInsert('agent_setting', agentSettingsData, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Delete in proper order to respect foreign key constraints
      // First delete staging_data records that reference agents
      await queryInterface.bulkDelete('staging_data', null, { transaction });
      // Then delete agent_setting records
      await queryInterface.bulkDelete('agent_setting', null, { transaction });
      // Finally delete agent records
      await queryInterface.bulkDelete('agent', null, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
